import { Body, Controller, Get, HttpException, HttpStatus, Post } from '@nestjs/common';
import { BasicHandlersService } from './basic-handlers.service';
import { SendMessageDto } from './dto/send-message.dto';
import type {
  SendMessageResponse,
  WhatsAppClientStatus,
  GetChatsResponse,
  GetContactsResponse,
} from './interfaces/basic-handlers.interface';

@Controller('basic')
export class BasicHandlersController {
  constructor(private readonly whatsAppService: BasicHandlersService) {}

  @Get('status')
  getStatus(): WhatsAppClientStatus {
    return this.whatsAppService.getStatus();
  }

  @Get('qr')
  getQRCode() {
    const qrCode = this.whatsAppService.getQRCode();
    return { success: true, data: { qrCode } };
  }

  @Post('send-message')
  async sendMessage(@Body() sendMessageDto: SendMessageDto): Promise<SendMessageResponse> {
    try {
      const result = await this.whatsAppService.sendMessage({
        to: sendMessageDto.to,
        message: sendMessageDto.message,
        type: sendMessageDto.type,
      });

      if (!result.success) {
        throw new HttpException(result.error || 'Failed to send message', HttpStatus.BAD_REQUEST);
      }

      return result;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Internal server error', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('send-group-message')
  async sendGroupMessage(@Body() sendMessageDto: SendMessageDto): Promise<SendMessageResponse> {
    try {
      // Force type to 'group' for this endpoint
      const result = await this.whatsAppService.sendMessage({
        to: sendMessageDto.to,
        message: sendMessageDto.message,
        type: 'group',
      });

      if (!result.success) {
        throw new HttpException(result.error || 'Failed to send message', HttpStatus.BAD_REQUEST);
      }

      return result;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Internal server error', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('restart')
  async restartClient(): Promise<{ message: string }> {
    try {
      await this.whatsAppService.restartClient();
      return { message: 'WhatsApp client restarted successfully' };
    } catch {
      throw new HttpException(
        'Failed to restart WhatsApp client',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('chats')
  async getChats(): Promise<GetChatsResponse> {
    try {
      const chats = await this.whatsAppService.getChats();
      return { success: true, data: chats };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message,
      };
    }
  }

  @Get('contacts')
  async getContacts(): Promise<GetContactsResponse> {
    try {
      const contacts = await this.whatsAppService.getContacts();
      return { success: true, data: contacts };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message,
      };
    }
  }
}
