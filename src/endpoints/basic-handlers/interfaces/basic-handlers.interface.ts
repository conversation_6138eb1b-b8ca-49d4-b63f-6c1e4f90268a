export interface WhatsAppClientStatus {
  isReady: boolean;
  qrCode?: string;
  clientInfo?: {
    pushname: string;
    wid: string;
    platform: string;
  };
}

export interface WhatsAppMessage {
  id: string;
  body: string;
  from: string;
  to: string;
  timestamp: number;
  type: string;
  isGroupMsg: boolean;
  author?: string;
  quotedMsgId?: string;
}

export interface SendMessageRequest {
  to: string;
  message: string;
  type: 'individual' | 'group';
}

export interface SendMessageResponse {
  success: boolean;
  messageId?: string;
  error?: string;
}

export interface WhatsAppContact {
  id: {
    _serialized: string;
    server: string;
    user: string;
  };
  name?: string;
  pushname?: string;
  shortName?: string;
  isBusiness?: boolean;
  isEnterprise?: boolean;
  [key: string]: any; // Allow additional properties
}

export interface WhatsAppChat {
  id: {
    _serialized: string;
    server: string;
    user: string;
  };
  name?: string;
  isGroup: boolean;
  isBusiness?: boolean;
  unreadCount: number;
  timestamp: number;
  archived: boolean;
  [key: string]: any; // Allow additional properties
}

export interface GetChatsResponse {
  success: boolean;
  data?: WhatsAppChat[];
  error?: string;
}

export interface GetContactsResponse {
  success: boolean;
  data?: WhatsAppContact[];
  error?: string;
}

export interface WhatsAppClientEvents {
  qr: (qr: string) => void;
  ready: () => void;
  authenticated: () => void;
  auth_failure: (message: string) => void;
  disconnected: (reason: string) => void;
  message: (message: any) => void;
}
