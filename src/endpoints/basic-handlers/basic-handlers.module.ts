import { Module } from '@nestjs/common';
import { BasicHandlersService } from './basic-handlers.service';
import { BasicHandlersController } from './basic-handlers.controller';
import { WhatsAppModule } from '../../modules/whatsapp/whatsapp.module';

@Module({
  imports: [WhatsAppModule],
  controllers: [BasicHandlersController],
  providers: [BasicHandlersService],
  exports: [BasicHandlersService],
})
export class BasicHandlersModule {}
