import { Injectable, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OnModuleInit } from '@nestjs/common';
import { Client, LocalAuth } from 'whatsapp-web.js';
import { WhatsAppClientStatus } from '../../endpoints/basic-handlers/interfaces/basic-handlers.interface';
import * as QRCode from 'qrcode';
import {
  WhatsAppSendMessageParams,
  WhatsAppContact,
  WhatsAppChat,
  WhatsAppMessage,
} from './interfaces/whatsapp.interface';
import { TrimitraAiService } from '../trimitra-ai/trimitra-ai.service';

@Injectable()
export class WhatsAppService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(WhatsAppService.name);
  private client: Client;
  private clientStatus: WhatsAppClientStatus = {
    isReady: false,
  };
  private qrCodeString: string;

  constructor(private readonly trimitraAiService: TrimitraAiService) {}

  async onModuleInit() {
    await this.initializeClient();
  }

  async onModuleDestroy() {
    if (this.client) {
      await this.client.destroy();
      this.logger.log('WhatsApp client destroyed');
    }
  }

  getStatus(): WhatsAppClientStatus {
    return this.clientStatus;
  }

  getQRCode(): string | null {
    return this.clientStatus.qrCode || null;
  }

  async sendMessage(params: WhatsAppSendMessageParams) {
    if (!this.clientStatus.isReady) {
      throw new Error('WhatsApp client is not ready');
    }

    const suffix = params.type === 'group' ? '@g.us' : '@c.us';
    const chatId = params.to.includes(suffix) ? params.to : `${params.to}${suffix}`;
    const message = await this.client.sendMessage(chatId, params.message);

    this.logger.log(`Message sent to ${params.to}: ${params.message}`);

    return {
      messageId: message.id._serialized,
    };
  }

  async restartClient(): Promise<void> {
    try {
      if (this.client) {
        await this.client.destroy();
      }
      this.clientStatus = { isReady: false };
      await this.initializeClient();
      this.logger.log('WhatsApp client restarted');
    } catch (error) {
      this.logger.error('Failed to restart client', error);
      throw error;
    }
  }

  async getChats(): Promise<WhatsAppChat[]> {
    if (!this.clientStatus.isReady) {
      throw new Error('WhatsApp client is not ready');
    }

    try {
      const chats = await this.client.getChats();
      return chats.map((chat) => {
        // Create a new object with the properties we want to expose
        const result: WhatsAppChat = {
          id: {
            _serialized: chat.id._serialized,
            server: chat.id.server,
            user: chat.id.user,
          },
          name: chat.name || chat.id.user,
          isGroup: chat.isGroup,
          unreadCount: chat.unreadCount || 0,
          timestamp: chat.timestamp || 0,
          archived: chat.archived || false,
        };

        // Conditionally add optional properties if they exist
        if ('isBusiness' in chat && typeof chat.isBusiness === 'boolean') {
          result.isBusiness = chat.isBusiness;
        }

        return result;
      });
    } catch (error) {
      this.logger.error('Failed to get chats', error);
      throw error;
    }
  }

  async getContacts(): Promise<WhatsAppContact[]> {
    if (!this.clientStatus.isReady) {
      throw new Error('WhatsApp client is not ready');
    }

    try {
      const contacts = await this.client.getContacts();
      return contacts.map((contact) => {
        // Create a new object with the properties we want to expose
        const result: WhatsAppContact = {
          id: {
            _serialized: contact.id._serialized,
            server: contact.id.server,
            user: contact.id.user,
          },
        };

        // Conditionally add optional properties if they exist
        if ('name' in contact && typeof contact.name === 'string') {
          result.name = contact.name;
        }
        if ('pushname' in contact && typeof contact.pushname === 'string') {
          result.pushname = contact.pushname;
        }
        if ('shortName' in contact && typeof contact.shortName === 'string') {
          result.shortName = contact.shortName;
        }
        if ('isBusiness' in contact && typeof contact.isBusiness === 'boolean') {
          result.isBusiness = contact.isBusiness;
        }
        if ('isEnterprise' in contact && typeof contact.isEnterprise === 'boolean') {
          result.isEnterprise = contact.isEnterprise;
        }

        return result;
      });
    } catch (error) {
      this.logger.error('Failed to get contacts', error);
      throw error;
    }
  }

  private async initializeClient() {
    try {
      this.client = new Client({
        authStrategy: new LocalAuth({
          clientId: 'whatsapp-session',
        }),
        puppeteer: {
          headless: true,
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--single-process',
            '--disable-gpu',
          ],
        },
      });

      this.setupEventListeners();
      await this.client.initialize();
      this.logger.log('WhatsApp client initialization started');
    } catch (error) {
      this.logger.error('Failed to initialize WhatsApp client', error);
      throw error;
    }
  }

  private setupEventListeners() {
    this.client.on('qr', (qr) => {
      this.logger.log('QR Code received');
      this.qrCodeString = qr;
      this.generateQRCodeDataURL(qr);
      this.printQRCodeToTerminal(qr);
    });

    this.client.on('ready', () => {
      this.logger.log('WhatsApp client is ready!');
      this.clientStatus.isReady = true;
      this.clientStatus.qrCode = undefined;

      const clientInfo = this.client.info;
      if (clientInfo) {
        this.clientStatus.clientInfo = {
          pushname: clientInfo.pushname,
          wid: clientInfo.wid._serialized,
          platform: clientInfo.platform,
        };
      }
    });

    this.client.on('authenticated', () => {
      this.logger.log('WhatsApp client authenticated');
    });

    this.client.on('auth_failure', (message) => {
      this.logger.error('Authentication failed', message);
      this.clientStatus.isReady = false;
    });

    this.client.on('disconnected', (reason) => {
      this.logger.warn('WhatsApp client disconnected', reason);
      this.clientStatus.isReady = false;
      this.clientStatus.qrCode = undefined;
    });

    this.client.on('message', (message: WhatsAppMessage) => {
      void this.handleMessage(message);
    });
  }

  private async handleMessage(message: WhatsAppMessage): Promise<void> {
    console.log(JSON.stringify(message, null, 2));
    this.logger.log(`Message received from ${message.from}`);

    // Prevent self-reply
    if (message.fromMe) {
      return;
    }

    // Simple ping-pong response
    if (message.body.toLowerCase() === '!ping') {
      void message.reply('pong');
      return;
    }

    // Check if message mentions the bot or contains trigger keywords
    const isMention = message.mentionedIds && message.mentionedIds.length > 0;
    const hasKeyword = this.containsTriggerKeywords(message.body);

    if (isMention || hasKeyword) {
      try {
        this.logger.log(`Processing AI request from ${message.from}`);

        // Send message to Trimitra AI
        const aiResponse = await this.trimitraAiService.sendChatMessage({
          message: {
            text: message.body,
            name: 'user',
          },
          user_name: message.from,
          thread_id: `whatsapp_${message.from}`,
        });

        // Reply with AI response
        if (aiResponse && aiResponse.data) {
          const responseText = aiResponse.data.data.agent_response;
          await message.reply(responseText);
          this.logger.log(`AI response sent to ${message.from}`);
        } else {
          await message.reply('Maaf, saya tidak dapat memproses permintaan Anda saat ini.');
        }
      } catch (error) {
        this.logger.error('Error processing AI request:', error);
        await message.reply('Maaf, terjadi kesalahan saat memproses permintaan Anda.');
      }
    }
  }

  private generateQRCodeDataURL(qr: string) {
    QRCode.toDataURL(qr, (error, url) => {
      if (error) {
        this.logger.error('Failed to generate QR code', error);
        return;
      }
      this.clientStatus.qrCode = url;
    });
  }

  private printQRCodeToTerminal(qr: string) {
    QRCode.toString(qr, { type: 'terminal', small: true }, (error, qrCode) => {
      if (error) {
        this.logger.error('Failed to print QR code to terminal', error);
        return;
      }
      console.log(qrCode);
    });
  }

  private containsTriggerKeywords(messageBody: string): boolean {
    const triggerKeywords = ['bot', 'ai', 'help', 'bantuan', 'tanya', 'ask'];
    const lowerCaseBody = messageBody.toLowerCase();
    return triggerKeywords.some((keyword) => lowerCaseBody.includes(keyword));
  }
}
