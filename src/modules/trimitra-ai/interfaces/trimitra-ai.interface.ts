export interface Message {
  text: string;
  name: string;
}

export interface ChatRequest {
  message: Message;
  user_name: string;
  thread_id: string;
}

export interface UsageMetadata {
  input_tokens: number;
  output_tokens: number;
  total_tokens: number;
}

export interface TrimitraData {
  agent_response: string;
  thread_id: string;
  user_name: string;
  message_count: number;
  usage_metadata: UsageMetadata;
}

export interface TrimitraApiResponse {
  status_code: number;
  success: boolean;
  message: string;
  data: TrimitraData;
  metadata: any;
}

export interface ChatResponse {
  success: boolean;
  data?: TrimitraApiResponse;
  error?: string;
}
