import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ChatResponse, TrimitraApiResponse } from './interfaces/trimitra-ai.interface';
import { ChatRequestDto } from './dto/chat-request.dto';

@Injectable()
export class TrimitraAiService {
  private readonly logger = new Logger(TrimitraAiService.name);
  private readonly baseUrl =
    process.env.NODE_ENV === 'development'
      ? 'http://127.0.0.1:8000'
      : 'https://trimitra-ai-agent-153502006543.asia-southeast2.run.app';

  constructor(private readonly httpService: HttpService) {}

  async sendChatMessage(chatRequest: ChatRequestDto): Promise<ChatResponse> {
    try {
      const url = `${this.baseUrl}/chat/trimitra`;

      this.logger.log(`Sending chat message to Trimitra AI: ${chatRequest.message.text}`);

      const response = await firstValueFrom(
        this.httpService.post(url, chatRequest, {
          headers: {
            'Content-Type': 'application/json',
          },
        })
      );

      this.logger.log('Successfully received response from Trimitra AI');

      return {
        success: true,
        data: response.data as TrimitraApiResponse,
      };
    } catch (error: any) {
      this.logger.error('Error sending message to Trimitra AI:', (error as Error)?.message);

      return {
        success: false,
        error: (error as Error)?.message || 'Failed to send message to Trimitra AI',
      };
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/health`, {
          timeout: 5000,
        })
      );

      return response.status === 200;
    } catch (error: any) {
      this.logger.error('Health check failed:', (error as Error)?.message);
      return false;
    }
  }
}
