import { IsString, <PERSON>NotEmpty, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class MessageDto {
  @IsString()
  @IsNotEmpty()
  text: string;

  @IsString()
  @IsNotEmpty()
  name: string;
}

export class ChatRequestDto {
  @ValidateNested()
  @Type(() => MessageDto)
  message: MessageDto;

  @IsString()
  @IsNotEmpty()
  user_name: string;

  @IsString()
  @IsNotEmpty()
  thread_id: string;
}
