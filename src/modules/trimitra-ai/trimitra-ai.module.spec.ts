import { Test, TestingModule } from '@nestjs/testing';
import { HttpModule } from '@nestjs/axios';
import { TrimitraAiModule } from './trimitra-ai.module';
import { TrimitraAiService } from './trimitra-ai.service';

describe('TrimitraAiModule', () => {
  let module: TestingModule;
  let service: TrimitraAiService;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [TrimitraAiModule, HttpModule],
    }).compile();

    service = module.get<TrimitraAiService>(TrimitraAiService);
  });

  afterEach(async () => {
    await module.close();
  });

  it('should be defined', () => {
    expect(module).toBeDefined();
  });

  it('should provide TrimitraAiService', () => {
    expect(service).toBeDefined();
    expect(service).toBeInstanceOf(TrimitraAiService);
  });

  it('should have proper module configuration', () => {
    const moduleRef = module.get(TrimitraAiModule);
    expect(moduleRef).toBeDefined();
  });

  describe('Service Integration', () => {
    it('should have sendChatMessage method', () => {
      expect(typeof service.sendChatMessage).toBe('function');
    });

    it('should have healthCheck method', () => {
      expect(typeof service.healthCheck).toBe('function');
    });
  });
});
