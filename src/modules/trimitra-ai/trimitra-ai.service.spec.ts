import { Test, TestingModule } from '@nestjs/testing';
import { HttpService } from '@nestjs/axios';
import { TrimitraAiService } from './trimitra-ai.service';
import { ChatRequestDto } from './dto/chat-request.dto';
import { of, throwError } from 'rxjs';
import { AxiosResponse, InternalAxiosRequestConfig } from 'axios';

describe('TrimitraAiService', () => {
  let service: TrimitraAiService;
  let httpService: HttpService;

  const mockHttpService = {
    post: jest.fn(),
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TrimitraAiService,
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
      ],
    }).compile();

    service = module.get<TrimitraAiService>(TrimitraAiService);
    httpService = module.get<HttpService>(HttpService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('sendChatMessage', () => {
    const mockChatRequest: ChatRequestDto = {
      message: {
        text: 'Hello, AI!',
        name: 'Test User',
      },
      user_name: 'testuser',
      thread_id: 'thread123',
    };

    it('should send chat message successfully', async () => {
      const mockTrimitraResponse = {
        status_code: 200,
        success: true,
        message: 'Agent Trimitra response generated successfully',
        data: {
          agent_response: 'Hello back!',
          thread_id: 'thread123',
          user_name: 'testuser',
          message_count: 1,
          usage_metadata: {
            input_tokens: 10,
            output_tokens: 5,
            total_tokens: 15,
          },
        },
        metadata: null,
      };

      const mockResponse: AxiosResponse = {
        data: mockTrimitraResponse,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {} as InternalAxiosRequestConfig,
      };

      mockHttpService.post.mockReturnValue(of(mockResponse));

      const result = await service.sendChatMessage(mockChatRequest);

      expect(result).toEqual({
        success: true,
        data: mockTrimitraResponse,
      });
      expect(mockHttpService.post).toHaveBeenCalledWith(
        expect.stringContaining('/chat/trimitra'),
        mockChatRequest,
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    });

    it('should handle error when sending chat message', async () => {
      const mockError = new Error('Network error');
      mockHttpService.post.mockReturnValue(throwError(() => mockError));

      const result = await service.sendChatMessage(mockChatRequest);

      expect(result).toEqual({
        success: false,
        error: 'Network error',
      });
    });

    it('should handle error without message', async () => {
      const mockError = {};
      mockHttpService.post.mockReturnValue(throwError(() => mockError));

      const result = await service.sendChatMessage(mockChatRequest);

      expect(result).toEqual({
        success: false,
        error: 'Failed to send message to Trimitra AI',
      });
    });
  });

  describe('healthCheck', () => {
    it('should return true when health check is successful', async () => {
      const mockResponse: AxiosResponse = {
        data: { status: 'ok' },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {} as InternalAxiosRequestConfig,
      };

      mockHttpService.get.mockReturnValue(of(mockResponse));

      const result = await service.healthCheck();

      expect(result).toBe(true);
      expect(mockHttpService.get).toHaveBeenCalledWith(expect.stringContaining('/health'), {
        timeout: 5000,
      });
    });

    it('should return false when health check fails', async () => {
      const mockError = new Error('Service unavailable');
      mockHttpService.get.mockReturnValue(throwError(() => mockError));

      const result = await service.healthCheck();

      expect(result).toBe(false);
    });

    it('should return false when status is not 200', async () => {
      const mockResponse: AxiosResponse = {
        data: { status: 'error' },
        status: 500,
        statusText: 'Internal Server Error',
        headers: {},
        config: {} as InternalAxiosRequestConfig,
      };

      mockHttpService.get.mockReturnValue(of(mockResponse));

      const result = await service.healthCheck();

      expect(result).toBe(false);
    });
  });

  describe('environment configuration', () => {
    it('should use development URL when NODE_ENV is development', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      // Create a new instance to test environment configuration
      const testService = new TrimitraAiService(httpService);

      // Access private property for testing
      const baseUrl = (testService as { baseUrl: string }).baseUrl;
      expect(baseUrl).toBe('http://127.0.0.1:8000');

      process.env.NODE_ENV = originalEnv;
    });

    it('should use production URL when NODE_ENV is not development', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      // Create a new instance to test environment configuration
      const testService = new TrimitraAiService(httpService);

      // Access private property for testing
      const baseUrl = (testService as { baseUrl: string }).baseUrl;
      expect(baseUrl).toBe('https://trimitra-ai-agent-153502006543.asia-southeast2.run.app');

      process.env.NODE_ENV = originalEnv;
    });
  });
});
