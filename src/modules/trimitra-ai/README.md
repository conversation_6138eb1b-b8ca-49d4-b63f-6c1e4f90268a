# Trimitra AI Module

Module NestJS untuk integrasi dengan layanan Trimitra AI. Module ini menyediakan service untuk berkomunikasi dengan API Trimitra AI.

## Fitur

- **Chat Integration**: Mengirim pesan ke Trimitra AI dan menerima respons
- **Health Check**: Memeriksa status kesehatan layanan Trimitra AI
- **Error Handling**: Penanganan error yang robust dengan logging
- **Environment-based Configuration**: Konfigurasi URL berbasis environment
- **Comprehensive Testing**: Unit test dan integration test lengkap

## Struktur Module

```
trimitra-ai/
├── dto/
│   └── chat-request.dto.ts      # Data Transfer Object untuk request chat
├── interfaces/
│   └── trimitra-ai.interface.ts # Interface untuk response
├── trimitra-ai.service.ts       # Service untuk logika bisnis
├── trimitra-ai.module.ts        # Module definition
├── trimitra-ai.service.spec.ts    # Unit test untuk service
├── trimitra-ai.module.spec.ts     # Integration test untuk module
└── README.md                    # Dokumentasi module
```

## Konfigurasi Environment

Module ini menggunakan konfigurasi berbasis environment untuk menentukan base URL:

- **Development** (`NODE_ENV=development`): `http://127.0.0.1:8000`
- **Production** (default): `https://trimitra-ai-agent-153502006543.asia-southeast2.run.app`

Untuk mengatur environment:

```bash
# Development
export NODE_ENV=development

# Production (default)
export NODE_ENV=production
```

## Instalasi dan Penggunaan

### 1. Import Module

```typescript
import { TrimitraAiModule } from './modules/trimitra-ai/trimitra-ai.module';

@Module({
  imports: [
    // ... other modules
    TrimitraAiModule,
  ],
})
export class AppModule {}
```

### 2. Menggunakan Service

```typescript
import { TrimitraAiService } from './modules/trimitra-ai';

@Injectable()
export class SomeService {
  constructor(private readonly trimitraAiService: TrimitraAiService) {}

  async sendMessage() {
    const chatRequest = {
      message: {
        text: "Jelaskan kepada saya tentang teknologi blockchain dalam bahasa yang mudah dipahami.",
        name: "Pengguna"
      },
      user_name: "Pengguna Indonesia",
      thread_id: "conv_id_001"
    };

    const response = await this.trimitraAiService.sendChatMessage(chatRequest);
    return response;
  }
}
```



## Testing

Module ini dilengkapi dengan comprehensive testing suite yang mencakup:

### Unit Tests

#### Service Tests (`trimitra-ai.service.spec.ts`)
- ✅ Test `sendChatMessage` method (success & error cases)
- ✅ Test `healthCheck` method (success & error cases)
- ✅ Test environment-based URL configuration
- ✅ Test HTTP error handling



### Integration Tests

#### Module Tests (`trimitra-ai.module.spec.ts`)
- ✅ Test module compilation
- ✅ Test service provisioning
- ✅ Test dependency injection
- ✅ Test module configuration

### Menjalankan Tests

```bash
# Menjalankan semua tests
npm test

# Menjalankan tests dengan coverage
npm run test:cov

# Menjalankan tests dalam watch mode
npm run test:watch

# Menjalankan tests untuk module tertentu
npm test -- --testPathPattern=trimitra-ai
```

### Test Coverage

Test suite mencakup:
- **Service Methods**: 100% coverage untuk semua public methods
- **Error Handling**: Test untuk berbagai skenario error
- **Environment Configuration**: Test untuk konfigurasi development dan production
- **Integration**: Test untuk integrasi antar komponen

## API Reference

### TrimitraAiService

#### `sendChatMessage(chatRequest: ChatRequestDto): Promise<ChatResponse>`

Mengirim pesan chat ke Trimitra AI.

**Parameters:**
- `chatRequest`: Object berisi pesan, nama pengguna, dan thread ID

**Returns:**
- `Promise<ChatResponse>`: Response dari Trimitra AI atau error

#### `healthCheck(): Promise<boolean>`

Memeriksa status kesehatan layanan Trimitra AI.

**Returns:**
- `Promise<boolean>`: `true` jika layanan sehat, `false` jika tidak



## Error Handling

Module ini menangani berbagai jenis error:

- **Network Errors**: Timeout, connection refused, dll.
- **HTTP Errors**: 4xx, 5xx status codes
- **Service Errors**: Error dari Trimitra AI service
- **Validation Errors**: Invalid request format

Semua error di-log menggunakan NestJS Logger dan dikembalikan dalam format yang konsisten.

## Logging

Module menggunakan NestJS Logger untuk mencatat:

- Request dan response chat messages
- Health check status
- Error details
- Service availability status

## Dependencies

- `@nestjs/common`: NestJS core functionality
- `@nestjs/axios`: HTTP client untuk komunikasi dengan API
- `rxjs`: Reactive programming utilities
- `class-validator`: Validasi DTO
- `class-transformer`: Transformasi object

## Troubleshooting

### Common Issues

1. **Connection Timeout**
   - Periksa network connectivity
   - Pastikan Trimitra AI service berjalan
   - Periksa firewall settings

2. **Invalid Response Format**
   - Periksa format request sesuai dengan DTO
   - Pastikan semua required fields terisi

3. **Environment Configuration**
   - Pastikan `NODE_ENV` diset dengan benar
   - Periksa URL configuration untuk environment yang sesuai

### Debug Mode

Untuk debugging, set log level ke debug:

```typescript
// main.ts
app.useLogger(['error', 'warn', 'log', 'debug', 'verbose']);
```