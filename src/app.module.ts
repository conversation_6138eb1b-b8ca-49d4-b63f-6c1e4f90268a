import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { BasicHandlersModule } from './endpoints/basic-handlers/basic-handlers.module';
import { WhatsAppModule } from './modules/whatsapp/whatsapp.module';
import { TrimitraAiModule } from './modules/trimitra-ai/trimitra-ai.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    WhatsAppModule,
    TrimitraAiModule,
    BasicHandlersModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
