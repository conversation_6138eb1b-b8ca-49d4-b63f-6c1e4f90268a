# <PERSON>wen Code Context for `whatsapp-headless-trimitra`

This document provides essential context for Qwen Code to effectively assist with the `whatsapp-headless-trimitra` project.

## Project Type

This is a **code project**. It is a server-side application built with the NestJS framework, integrating with WhatsApp Web via the `whatsapp-web.js` library.

## Project Overview

**Name:** WhatsApp Headless Trimitra

**Description:** A headless WhatsApp client that exposes a REST API for programmatic interaction with WhatsApp. Key features include sending messages, receiving messages (via logging), QR code authentication, and automatic session management.

**Main Technologies:**
*   **Framework:** NestJS (Node.js/TypeScript)
*   **WhatsApp Integration:** `whatsapp-web.js`
*   **Browser Automation:** Puppeteer (used internally by `whatsapp-web.js`)
*   **QR Code Handling:** `qrcode` library
*   **Configuration:** `@nestjs/config`, `.env` files

## Project Structure

```
src/
├── main.ts                # Application entry point
├── app.module.ts          # Root application module
├── app.controller.ts      # Main application controller
├── app.service.ts         # Main application service
├── endpoints/             # API endpoint modules
│   └── basic-handlers/    # Basic API handlers (status, QR, send message, restart)
│       ├── basic-handlers.module.ts
│       ├── basic-handlers.controller.ts
│       ├── basic-handlers.service.ts
│       ├── dto/           # Data Transfer Objects for API requests
│       └── interfaces/    # TypeScript interfaces for this module
└── modules/               # Core logic modules
    └── whatsapp/          # Core WhatsApp client logic
        ├── whatsapp.module.ts
        ├── whatsapp.service.ts
        └── interfaces/    # TypeScript interfaces for this module
```

*   `package.json`: Defines dependencies, devDependencies, and NPM scripts.
*   `nest-cli.json`: NestJS CLI configuration.
*   `tsconfig.json`: TypeScript compiler options.
*   `.env.example`: Example environment variables.
*   `.wwebjs_auth/`, `.wwebjs_cache/`: Directories used by `whatsapp-web.js` for session persistence and caching.

## Building and Running

*   **Install Dependencies:** `npm install`
*   **Development Mode:** `npm run start:dev` (Starts the app with hot-reloading)
*   **Build for Production:** `npm run build` (Outputs compiled code to `dist/`)
*   **Run Production Build:** `npm run start:prod` (Runs the compiled app from `dist/`)
*   **Linting:** `npm run lint` (Checks code style)
*   **Formatting:** `npm run format` (Formats code with Prettier)
*   **Testing:** `npm run test` (Runs unit tests with Jest)

## API Endpoints (`/basic`)

*   `GET /basic/status`: Get the current status of the WhatsApp client (ready, QR code, client info).
*   `GET /basic/qr`: Get the QR code data URL for authentication (if not already authenticated).
*   `POST /basic/send-message`: Send a message to a specified phone number.
    *   Body: `{ "to": "6281234567890", "message": "Hello!" }`
*   `POST /basic/restart`: Restart the WhatsApp client instance.

## Development Conventions

*   **Framework:** NestJS, utilizing modules, controllers, services, and dependency injection.
*   **Language:** TypeScript.
*   **Code Style:** Likely managed by ESLint and Prettier (configured in `eslint.config.mjs`, `.prettierrc`).
*   **Testing:** Jest is configured for testing.
*   **Configuration:** Uses `@nestjs/config` to load environment variables from `.env` files.
*   **Module Structure:** Core logic is separated into distinct modules (`whatsapp`) and API endpoints are grouped into modules (`basic-handlers`).

## Key Files for Context

*   `README.md`: Contains detailed project description, features, setup, and API usage.
*   `package.json`: Lists dependencies and defines NPM scripts.
*   `src/app.module.ts`: The root module that imports other modules.
*   `src/modules/whatsapp/whatsapp.service.ts`: Contains the core logic for interacting with the WhatsApp client.
*   `src/endpoints/basic-handlers/basic-handlers.controller.ts`: Defines the REST API endpoints for basic actions.
*   `src/endpoints/basic-handlers/basic-handlers.service.ts`: Implements the logic behind the basic API endpoints, interfacing with the WhatsApp service.