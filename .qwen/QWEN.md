# WhatsApp Headless Trimitra - Qwen Code Context

This document provides essential information for Qwen Code to effectively assist with the `whatsapp-headless-trimitra` project. This is a NestJS-based application that provides a headless WhatsApp client via a REST API.

## Project Type

This is a **Node.js code project** using the **NestJS framework**. The primary purpose is to create a server-side application for programmatic interaction with WhatsApp.

## Project Overview

- **Name**: WhatsApp Headless Trimitra
- **Description**: A server-side application that allows you to interact with WhatsApp programmatically through a REST API. It uses `whatsapp-web.js` with a headless browser to maintain a WhatsApp session.
- **Core Technologies**:
  - [NestJS](https://nestjs.com/): Main application framework.
  - [whatsapp-web.js](https://github.com/pedroslopez/whatsapp-web.js): Library for interacting with the WhatsApp Web API.
  - [Puppeteer](https://pptr.dev/): Used by `whatsapp-web.js` for headless browser automation.
  - [QRCode](https://github.com/soldair/node-qrcode): For generating QR codes for authentication.
- **Architecture**:
  - Built using NestJS modules (`AppModule`, `WhatsAppModule`, `BasicHandlersModule`).
  - Core logic for WhatsApp interaction resides in `WhatsAppService`.
  - REST API endpoints are defined in `BasicHandlersController`.
  - Session persistence is handled automatically by `whatsapp-web.js` using `LocalAuth` in the `.wwebjs_auth` directory.

## Running and Development Commands

- **Install Dependencies**: `npm install`
- **Run in Development Mode**: `npm run start:dev`
- **Build for Production**: `npm run build`
- **Run in Production Mode**: `npm run start:prod` (after building)
- **Lint Code**: `npm run lint`
- **Format Code**: `npm run format`
- **Run Tests**: `npm run test` (unit), `npm run test:e2e` (end-to-end)
- **Default Port**: 3000 (configurable via `PORT` environment variable in `.env`).

## Key Source Files

- `src/main.ts`: Application entry point.
- `src/app.module.ts`: Root application module.
- `src/modules/whatsapp/whatsapp.service.ts`: Core service for initializing the WhatsApp client, managing its state, and sending messages. Handles events like `qr`, `ready`, `authenticated`, `auth_failure`, `disconnected`, and incoming `message`.
- `src/endpoints/basic-handlers/basic-handlers.controller.ts`: Defines the REST API endpoints (`/basic/*`) for status, QR code retrieval, sending messages, and restarting the client.
- `package.json`: Defines project dependencies, scripts, and metadata.
- `nest-cli.json`: NestJS CLI configuration.
- `tsconfig.json`: TypeScript compiler configuration.

## Development Conventions

- **Framework**: NestJS, following its modular architecture and dependency injection patterns.
- **Language**: TypeScript.
- **Code Style**: Configured with ESLint and Prettier (see `eslint.config.mjs`, `.prettierrc`).
- **Environment Configuration**: Uses `@nestjs/config`. Environment variables are loaded from `.env` (example in `.env.example`).
- **Session Management**: Automatic session persistence using `whatsapp-web.js`'s `LocalAuth` strategy. Session data is stored in `.wwebjs_auth`.
- **Logging**: Uses NestJS's built-in `Logger`.
- **Testing**: Jest is configured for unit and e2e testing (scripts available in `package.json`).