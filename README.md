# WhatsApp Headless Trimitra

<p align="center">
  <img src="https://nestjs.com/img/logo-small.svg" width="120" alt="Nest Logo" />
</p>

A headless WhatsApp client built with NestJS and whatsapp-web.js for automated messaging and WhatsApp integration.

## Description

WhatsApp Headless Trimitra is a server-side application that allows you to interact with WhatsApp programmatically through a REST API. It uses whatsapp-web.js under the hood with a headless browser to maintain a WhatsApp session, enabling features like:

- Sending messages programmatically
- Receiving messages with webhook-like functionality
- Automatic session management
- QR code authentication

## Features

- ✅ Headless WhatsApp integration
- 🚀 RESTful API endpoints
- 🔐 Automatic session persistence
- 📱 QR code authentication
- 📨 Send messages to contacts/numbers
- 🔄 Client restart functionality
- 📊 Status monitoring

## Prerequisites

- Node.js (>= 16.x)
- npm/yarn
- A WhatsApp account (for authentication)

## Installation

```bash
# Clone the repository
git clone <repository-url>

# Navigate to project directory
cd whatsapp-headless-trimitra

# Install dependencies
npm install
```

## Running the Application

```bash
# Development mode
npm run start:dev

# Production mode
npm run build
npm run start:prod
```

By default, the application will start on port 3000. You can change this by setting the `PORT` environment variable.

## API Endpoints

### Get Client Status
```http
GET /basic/status
```
Returns the current status of the WhatsApp client.

### Get QR Code
```http
GET /basic/qr
```
Returns the QR code needed for authentication (if not already authenticated).

### Send Message
```http
POST /basic/send-message
Content-Type: application/json

{
  "to": "*************",  # Phone number with country code
  "message": "Hello from WhatsApp Headless!"
}
```

### Restart Client
```http
POST /basic/restart
```
Restarts the WhatsApp client (useful for reconnecting if disconnected).

## Authentication

1. Start the application
2. Make a GET request to `/basic/qr` to get the QR code data URL
3. Scan the QR code using your WhatsApp mobile app
4. The client will be authenticated and ready to use

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
PORT=3000  # Optional: Change default port
```

## Project Structure

```
src/
├── app.controller.ts      # Main application controller
├── app.module.ts          # Main application module
├── app.service.ts         # Main application service
├── main.ts                # Application entry point
└── basic-handlers/        # Basic handlers module
    ├── basic-handlers.module.ts # Basic handlers module definition
    ├── basic-handlers.service.ts # Basic handlers service with core logic
    ├── basic-handlers.controller.ts # Basic handlers API endpoints
    ├── dto/               # Data transfer objects
    └── interfaces/        # TypeScript interfaces
```

## Technologies Used

- [NestJS](https://nestjs.com/) - Progressive Node.js framework
- [whatsapp-web.js](https://github.com/pedroslopez/whatsapp-web.js) - WhatsApp Web API
- [Puppeteer](https://pptr.dev/) - Headless browser automation
- [QRCode](https://github.com/soldair/node-qrcode) - QR code generation

## Troubleshooting

### Common Issues

1. **QR Code not showing**: Ensure the application is running and make a request to `/whatsapp/qr`
2. **Client disconnected**: Use the restart endpoint to reconnect
3. **Message sending fails**: Check if the client is ready by checking the status endpoint

### Session Management

The application automatically saves and restores your WhatsApp session in the `.wwebjs_auth/session-whatsapp-session` directory. Do not delete this folder unless you want to re-authenticate.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a pull request

## Support

For support, please open an issue in the GitHub repository.