# Dependecies
/node_modules

# Compiled Output
/dist
/build

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store
Thumbs.db

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/*.code-snippets

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# Temp directory
.temp
.tmp

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# NestJS
.nest

# Editor
*.swp
*.swo

# Package lock files
package-lock.json
yarn.lock

# WhatsApp Web JS
/.wwebjs_auth
/.wwebjs_cache
