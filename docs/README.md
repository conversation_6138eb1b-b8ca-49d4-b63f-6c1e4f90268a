# WhatsApp Headless Trimitra Documentation

Welcome to the documentation for WhatsApp Headless Trimitra, a server-side application that allows you to interact with WhatsApp programmatically through a REST API.

## Table of Contents

1. [API Documentation](./api/)
   - [Endpoint Documentation](./api/endpoints/)
     - [Get Client Status](./api/endpoints/status.md)
     - [Get QR Code](./api/endpoints/qr.md)
     - [Send Message](./api/endpoints/send-message.md)
     - [Send Group Message](./api/endpoints/send-group-message.md)
     - [Get Chats](./api/endpoints/get-chats.md)
     - [Get Contacts](./api/endpoints/get-contacts.md)
     - [Restart Client](./api/endpoints/restart.md)

## Project Overview

WhatsApp Headless Trimitra is built with NestJS and uses whatsapp-web.js under the hood with a headless browser to maintain a WhatsApp session. It provides a RESTful API for common WhatsApp operations.

### Features
- Headless WhatsApp integration
- RESTful API endpoints
- Automatic session persistence
- QR code authentication
- Send messages to contacts/numbers
- Send messages to groups
- Get recent chats (including group IDs)
- Get contacts information
- Client restart functionality
- Status monitoring

## Quick Start

1. Install dependencies:
   ```bash
   npm install
   ```

2. Start the application:
   ```bash
   npm run start:dev
   ```

3. Authenticate by scanning the QR code (see API documentation for details)

4. Begin using the API endpoints to interact with WhatsApp

## Technologies Used
- [NestJS](https://nestjs.com/) - Progressive Node.js framework
- [whatsapp-web.js](https://github.com/pedroslopez/whatsapp-web.js) - WhatsApp Web API
- [Puppeteer](https://pptr.dev/) - Headless browser automation
- [QRCode](https://github.com/soldair/node-qrcode) - QR code generation

## Support
For support, please open an issue in the GitHub repository.