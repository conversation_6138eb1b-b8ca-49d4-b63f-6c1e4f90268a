# Get Client Status Endpoint

## Endpoint
```
GET /basic/status
```

## Description
Returns the current status of the WhatsApp client including authentication status and client information.

## Response

### Success Response
```json
{
  "isReady": true,
  "clientInfo": {
    "pushname": "<PERSON>",
    "wid": "<EMAIL>",
    "platform": "android"
  }
}
```

### Fields
| Field | Type | Description |
|-------|------|-------------|
| isReady | boolean | Indicates if the WhatsApp client is ready and authenticated |
| clientInfo | object | Information about the authenticated client (only present when isReady is true) |
| clientInfo.pushname | string | The display name of the WhatsApp account |
| clientInfo.wid | string | The WhatsApp ID of the account |
| clientInfo.platform | string | The platform of the WhatsApp account (android, ios, web, etc.) |

### Not Authenticated Response
```json
{
  "isReady": false
}
```

## Example Usage
```bash
curl -X GET http://localhost:3000/basic/status
```

## Common Issues
- If `isReady` is `false`, you need to authenticate the client using the QR code endpoint
- Client information is only available when the client is fully authenticated