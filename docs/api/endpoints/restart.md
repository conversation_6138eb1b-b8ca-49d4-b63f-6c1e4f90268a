# Restart Client Endpoint

## Endpoint
```
POST /basic/restart
```

## Description
Restarts the WhatsApp client. This is useful when the client is disconnected or experiencing issues. It will destroy the current client instance and create a new one, requiring re-authentication if the session was not persisted.

## Request

### Headers
```
Content-Type: application/json
```

### Body
No request body is required for this endpoint.

## Response

### Success Response
```json
{
  "message": "WhatsApp client restarted successfully"
}
```

### Fields
| Field | Type | Description |
|-------|------|-------------|
| message | string | Confirmation message that the client was restarted |

### Error Response
```json
{
  "statusCode": 500,
  "message": "Failed to restart WhatsApp client",
  "error": "Internal Server Error"
}
```

## Example Usage
```bash
curl -X POST http://localhost:3000/basic/restart
```

## When to Use
- When the client shows as disconnected in the status endpoint
- When you need to force a new authentication session
- When experiencing issues with message sending
- When the QR code has expired and you need a new one

## What Happens During Restart
1. The current WhatsApp client instance is destroyed
2. A new client instance is created with the same authentication strategy
3. Event listeners are re-attached to the new client
4. The client begins initialization process
5. A new QR code will be generated if authentication is required

## Common Issues
- Session files may be corrupted, requiring manual deletion of `.wwebjs_auth` directory
- After restart, you'll need to authenticate again using the QR code endpoint
- Restarting too frequently may cause temporary blocking by WhatsApp