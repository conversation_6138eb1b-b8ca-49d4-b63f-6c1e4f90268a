# Get Contacts Endpoint

## Endpoint
```
GET /basic/contacts
```

## Description
Returns a list of all contacts from the authenticated WhatsApp account. Each contact object contains information such as the contact ID, name, pushname, and other metadata.

This endpoint is useful for getting information about contacts, which can then be used with the send-message endpoint.

## Response

### Success Response
```json
{
  "success": true,
  "data": [
    {
      "id": {
        "serialized": "<EMAIL>",
        "server": "c.us",
        "user": "*************"
      },
      "name": "<PERSON>",
      "pushname": "<PERSON>",
      "shortName": "<PERSON>",
      "isBusiness": false,
      "isEnterprise": false
    },
    {
      "id": {
        "serialized": "<EMAIL>",
        "server": "c.us",
        "user": "*************"
      },
      "name": "<PERSON>",
      "pushname": "<PERSON>",
      "isBusiness": true,
      "isEnterprise": false
    }
  ]
}
```

### Fields
| Field | Type | Description |
|-------|------|-------------|
| success | boolean | Indicates if the request was successful |
| data | array | Array of contact objects |
| data[].id | object | The contact ID information |
| data[].id.serialized | string | The full serialized ID of the contact |
| data[].id.server | string | The server part of the ID (@c.us for individuals) |
| data[].id.user | string | The user identifier |
| data[].name | string | The display name of the contact (optional) |
| data[].pushname | string | The push name of the contact (optional) |
| data[].shortName | string | The short name of the contact (optional) |
| data[].isBusiness | boolean | Whether the contact is a business account (optional) |
| data[].isEnterprise | boolean | Whether the contact is an enterprise account (optional) |

### Error Response
```json
{
  "success": false,
  "error": "WhatsApp client is not ready"
}
```

## Example Usage
```bash
curl -X GET http://localhost:3000/basic/contacts
```

## Using Contact Information
To send a message to a contact, use the `id.serialized` field as the `to` parameter in the send-message endpoint.

Example JavaScript code to find a contact and send a message:
```javascript
const response = await fetch('http://localhost:3000/basic/contacts');
const data = await response.json();

if (data.success) {
  const contact = data.data.find(c => c.name === 'John Doe');
  if (contact) {
    const sendMessageResponse = await fetch('http://localhost:3000/basic/send-message', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        to: contact.id.serialized,
        message: 'Hello from WhatsApp Headless!',
        type: 'individual'
      })
    });
    
    console.log('Message sent:', await sendMessageResponse.json());
  }
}
```

## Common Issues
- If `success` is `false`, the client might not be authenticated yet
- Some fields like `name`, `pushname`, `shortName`, `isBusiness`, and `isEnterprise` may be undefined if not available for the contact
- The data array may be empty if there are no contacts in the WhatsApp account