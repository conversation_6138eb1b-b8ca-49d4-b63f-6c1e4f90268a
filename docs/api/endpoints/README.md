# API Endpoints Documentation

This directory contains documentation for all available API endpoints in the WhatsApp Headless Trimitra application.

## Available Endpoints

1. [Get Client Status](./status.md) - Check the authentication status of the WhatsApp client
2. [Get QR Code](./qr.md) - Retrieve the QR code for WhatsApp authentication
3. [Send Message](./send-message.md) - Send a text message to a WhatsApp number or group
4. [Send Group Message](./send-group-message.md) - Send a text message specifically to a WhatsApp group
5. [Get Chats](./get-chats.md) - Retrieve all recent chats (including group IDs)
6. [Get Contacts](./get-contacts.md) - Retrieve all contacts
7. [Restart Client](./restart.md) - Restart the WhatsApp client instance

## Base URL
All endpoints are relative to the base URL of your application, typically:
```
http://localhost:3000
```

## Authentication
Most endpoints require the WhatsApp client to be authenticated first. Follow this flow:

1. Start the application
2. Call the QR code endpoint to get the authentication QR code
3. Scan the QR code with your WhatsApp mobile app
4. Check the status endpoint to confirm authentication
5. Use other endpoints as needed

## Error Handling
All endpoints follow standard HTTP status codes:
- `200` - Success
- `400` - Bad Request (validation errors, client not ready)
- `500` - Internal Server Error (server-side issues)

## Rate Limiting
Currently, there is no built-in rate limiting. However, sending messages too frequently may result in temporary blocking by WhatsApp.