# Send Message Endpoint

## Endpoint
```
POST /basic/send-message
```

## Description
Sends a text message to a specified WhatsApp number or group. The client must be authenticated before using this endpoint.

For convenience, there is also a dedicated [Send Group Message](./send-group-message.md) endpoint that automatically sets the message type to "group".

## Request

### Headers
```
Content-Type: application/json
```

### Body
```json
{
  "to": "*************",
  "message": "Hello from WhatsApp Headless!",
  "type": "individual"
}
```

### Fields
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| to | string | Yes | The recipient's phone number with country code (without '+' or '0' prefix) or group ID |
| message | string | Yes | The text message to send |
| type | string | Yes | The type of recipient, either "individual" for personal chats or "group" for group chats |

## Response

### Success Response
```json
{
  "success": true,
  "messageId": "3A312309F3D80080EE1522D6B64E747D"
}
```

### Fields
| Field | Type | Description |
|-------|------|-------------|
| success | boolean | Indicates if the message was sent successfully |
| messageId | string | The unique identifier of the sent message |

### Error Response
```json
{
  "statusCode": 400,
  "message": "Failed to send message",
  "error": "Bad Request"
}
```

## Example Usage
```bash
curl -X POST http://localhost:3000/basic/send-message \
  -H "Content-Type: application/json" \
  -d '{
    "to": "*************",
    "message": "Hello from WhatsApp Headless!",
    "type": "individual"
  }'
```

## Validation Rules
- `to` must be a non-empty string
- `message` must be a non-empty string
- `type` must be either "individual" or "group"
- The phone number should include country code without '+' prefix

## Common Issues
- Sending messages will fail if the client is not ready (`isReady` is false)
- Some numbers may not receive messages if they have blocked the account or have privacy settings
- For group messages, the `to` field should contain the group ID, and `type` must be set to "group"
- Long messages might be split by WhatsApp automatically
```