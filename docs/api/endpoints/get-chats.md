# Get Chats Endpoint

## Endpoint
```
GET /basic/chats
```

## Description
Returns a list of all recent chats from the authenticated WhatsApp account. This includes both individual chats and group chats. Each chat object contains information such as the chat ID, name, whether it's a group, unread message count, and other metadata.

This endpoint is particularly useful for getting group IDs, which can then be used with the send-group-message endpoint.

## Response

### Success Response
```json
{
  "success": true,
  "data": [
    {
      "id": {
        "serialized": "<EMAIL>",
        "server": "c.us",
        "user": "*************"
      },
      "name": "<PERSON>",
      "isGroup": false,
      "isBusiness": false,
      "unreadCount": 0,
      "timestamp": **********,
      "archived": false
    },
    {
      "id": {
        "serialized": "<EMAIL>",
        "server": "g.us",
        "user": "*********"
      },
      "name": "Team Group",
      "isGroup": true,
      "isBusiness": false,
      "unreadCount": 5,
      "timestamp": **********,
      "archived": false
    }
  ]
}
```

### Fields
| Field | Type | Description |
|-------|------|-------------|
| success | boolean | Indicates if the request was successful |
| data | array | Array of chat objects |
| data[].id | object | The chat ID information |
| data[].id.serialized | string | The full serialized ID of the chat |
| data[].id.server | string | The server part of the ID (@c.us for individuals, @g.us for groups) |
| data[].id.user | string | The user or group identifier |
| data[].name | string | The display name of the chat |
| data[].isGroup | boolean | Whether the chat is a group chat |
| data[].isBusiness | boolean | Whether the chat is a business account (optional) |
| data[].unreadCount | number | Number of unread messages in the chat |
| data[].timestamp | number | Timestamp of the last message |
| data[].archived | boolean | Whether the chat is archived |

### Error Response
```json
{
  "success": false,
  "error": "WhatsApp client is not ready"
}
```

## Example Usage
```bash
curl -X GET http://localhost:3000/basic/chats
```

## Finding Group IDs
To find group IDs specifically, filter the results where `isGroup` is `true`. The group ID is in the `id.serialized` field and ends with `@g.us`.

Example JavaScript code to extract group IDs:
```javascript
const response = await fetch('http://localhost:3000/basic/chats');
const data = await response.json();

if (data.success) {
  const groupChats = data.data.filter(chat => chat.isGroup);
  const groupIds = groupChats.map(chat => chat.id.serialized);
  
  console.log('Group IDs:', groupIds);
}
```

## Common Issues
- If `success` is `false`, the client might not be authenticated yet
- The data array may be empty if there are no recent chats
- Some fields like `isBusiness` may be undefined if not applicable to the chat