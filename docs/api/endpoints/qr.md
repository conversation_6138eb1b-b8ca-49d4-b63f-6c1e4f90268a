# Get QR Code Endpoint

## Endpoint
```
GET /basic/qr
```

## Description
Returns the QR code needed for WhatsApp authentication when the client is not yet authenticated. The QR code is returned as a Data URL that can be displayed in an image tag.

## Response

### Success Response
```json
{
  "success": true,
  "data": {
    "qrCode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsCAAAAAA..."
  }
}
```

### Fields
| Field | Type | Description |
|-------|------|-------------|
| success | boolean | Indicates if the request was successful |
| data | object | Container for the QR code data |
| data.qrCode | string | Base64 encoded Data URL of the QR code image |

### No QR Code Available
```json
{
  "success": true,
  "data": {
    "qrCode": null
  }
}
```

## Example Usage
```bash
curl -X GET http://localhost:3000/basic/qr
```

## Authentication Flow
1. Start the application
2. Call this endpoint to get the QR code
3. Display the QR code Data URL in an `<img>` tag or decode it to show the image
4. Scan the QR code using your WhatsApp mobile app
5. After successful scanning, the client will be authenticated

## Common Issues
- If `qrCode` is `null`, the client might already be authenticated
- QR codes are time-sensitive and may expire after a few minutes
- If the QR code expires, restart the client using the restart endpoint to get a new QR code