# Send Group Message Endpoint\n\n## Endpoint\n```\nPOST /basic/send-group-message\n```\n\n## Description\nSends a text message specifically to a WhatsApp group. This endpoint is a convenience method that automatically sets the message type to \"group\". The client must be authenticated before using this endpoint.\n\n## Request\n\n### Headers\n```\nContent-Type: application/json\n```\n\n### Body\n```json\n{\n  \"to\": \"<EMAIL>\",\n  \"message\": \"Hello Group from WhatsApp Headless!\"\n}\n```\n\n### Fields\n| Field | Type | Required | Description |\n|-------|------|----------|-------------|\n| to | string | Yes | The group ID (ending with @g.us) |\n| message | string | Yes | The text message to send |\n\n## Response\n\n### Success Response\n```json\n{\n  \"success\": true,\n  \"messageId\": \"<EMAIL>\"\n}\n```\n\n### Fields\n| Field | Type | Description |\n|-------|------|-------------|\n| success | boolean | Indicates if the message was sent successfully |\n| messageId | string | The unique identifier of the sent message |\n\n### Error Response\n```json\n{\n  \"statusCode\": 400,\n  \"message\": \"Failed to send message\",\n  \"error\": \"Bad Request\"\n}\n```\n\n## Example Usage\n```bash\ncurl -X POST http://localhost:3000/basic/send-group-message \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\n    \"to\": \"<EMAIL>\",\n    \"message\": \"Hello Group from WhatsApp Headless!\"\n  }'\n```\n\n## Validation Rules\n- `to` must be a non-empty string and should be a valid group ID (ending with @g.us)\n- `message` must be a non-empty string\n\n## How it differs from Send Message\nThis endpoint is a specialized version of the [Send Message](./send-message.md) endpoint that automatically sets the message type to \"group\". It's equivalent to calling the send-message endpoint with:\n```json\n{\n  \"to\": \"<EMAIL>\",\n  \"message\": \"Hello Group from WhatsApp Headless!\",\n  \"type\": \"group\"\n}\n```\n\n## Common Issues\n- Sending messages will fail if the client is not ready (`isReady` is false)\n- You must be a member of the group to send messages to it\n- The group ID must be valid and end with \"@g.us\"\n- Long messages might be split by WhatsApp automatically\n