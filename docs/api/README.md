# WhatsApp Headless Trimitra API Documentation

Welcome to the API documentation for WhatsApp Headless Trimitra. This documentation covers all available endpoints to interact with the WhatsApp client programmatically.

## Table of Contents

1. [Endpoint Documentation](./endpoints/)
   - [Get Client Status](./endpoints/status.md)
   - [Get QR Code](./endpoints/qr.md)
   - [Send Message](./endpoints/send-message.md)
   - [Send Group Message](./endpoints/send-group-message.md)
   - [Get Chats](./endpoints/get-chats.md)
   - [Get Contacts](./endpoints/get-contacts.md)
   - [Restart Client](./endpoints/restart.md)

## Base URL
All endpoints are relative to the base URL of your application:
```
http://localhost:3000
```

The port can be configured using the `PORT` environment variable.

## Authentication Flow
To use the WhatsApp API, you need to authenticate the client first:

1. Start the application
2. Make a GET request to `/basic/qr` to retrieve the QR code
3. Display and scan the QR code with your WhatsApp mobile app
4. Check authentication status with `/basic/status`
5. Once authenticated, you can use other endpoints

## Common Request/Response Formats

### JSON
All endpoints use JSON for request and response bodies:
```http
Content-Type: application/json
```

### Success Responses
Success responses vary by endpoint but generally follow REST conventions.

### Error Responses
Error responses follow a standard format:
```json
{
  "statusCode": 400,
  "message": "Error description",
  "error": "Bad Request"
}
```

## HTTP Status Codes
- `200` - Success
- `400` - Bad Request (validation errors, client not ready)
- `404` - Not Found
- `500` - Internal Server Error

## Rate Limiting
There is currently no built-in rate limiting, but WhatsApp may impose its own limits. Use the API responsibly.

## Webhook-like Functionality
The application automatically responds to incoming messages with a simple ping-pong mechanism:
- When a message with content "!ping" is received, the system automatically replies with "pong"

For more complex webhook functionality, you would need to extend the message event handler in the WhatsApp service.